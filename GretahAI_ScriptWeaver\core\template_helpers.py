"""
Helper functions for the Script Template Manager (Stage 10).

This module provides functions for:
1. Loading and filtering optimized scripts for template use
2. Managing test case selection for template-based generation
3. Handling template metadata and display
4. Supporting template-based script generation workflows
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)


def get_optimized_scripts_for_templates(script_storage) -> List[Dict[str, Any]]:
    """
    Retrieve all optimized scripts suitable for use as templates.
    
    Args:
        script_storage: The script storage instance
        
    Returns:
        List of optimized script dictionaries with metadata
    """
    try:
        # Get all scripts from storage
        all_scripts = script_storage.get_all_scripts(include_current_session=True)
        
        # Filter for optimized scripts only
        optimized_scripts = [
            script for script in all_scripts 
            if script.get('type') == 'optimized' and 
               script.get('optimization_status') == 'optimized'
        ]
        
        # Sort by creation date (newest first)
        optimized_scripts.sort(
            key=lambda x: x.get('timestamp', datetime.min), 
            reverse=True
        )
        
        logger.info(f"Found {len(optimized_scripts)} optimized scripts for templates")
        return optimized_scripts
        
    except Exception as e:
        logger.error(f"Failed to retrieve optimized scripts: {e}")
        return []


def format_template_script_display(script: Dict[str, Any]) -> Dict[str, str]:
    """
    Format optimized script metadata for template selection display.
    
    Args:
        script: Script dictionary with metadata
        
    Returns:
        Dictionary of formatted display information
    """
    try:
        # Extract basic information
        test_case_id = script.get('test_case_id', 'Unknown')
        script_id = script.get('id', 'Unknown')
        timestamp = script.get('timestamp', datetime.now())
        
        # Format timestamp
        if isinstance(timestamp, str):
            try:
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            except:
                timestamp = datetime.now()
        
        formatted_timestamp = timestamp.strftime('%Y-%m-%d %H:%M:%S')
        
        # Get script content info
        content = script.get('content', '')
        line_count = len(content.splitlines()) if content else 0
        char_count = len(content) if content else 0
        
        # Extract metadata
        metadata = script.get('metadata', {})
        optimization_duration = metadata.get('optimization_duration', 'Unknown')
        
        display_info = {
            'title': f"Template: {test_case_id}",
            'subtitle': f"Optimized Script (ID: {script_id[:8]}...)",
            'timestamp': formatted_timestamp,
            'size_info': f"{line_count} lines, {char_count} characters",
            'optimization_info': f"Optimization duration: {optimization_duration}s" if optimization_duration != 'Unknown' else "Optimization duration: Unknown",
            'test_case_id': test_case_id,
            'script_id': script_id
        }
        
        return display_info
        
    except Exception as e:
        logger.error(f"Failed to format template script display: {e}")
        return {
            'title': "Unknown Template",
            'subtitle': "Error formatting display",
            'timestamp': "Unknown",
            'size_info': "Unknown size",
            'optimization_info': "Unknown optimization",
            'test_case_id': "Unknown",
            'script_id': "Unknown"
        }


def get_available_test_cases(state) -> List[Dict[str, Any]]:
    """
    Get all available test cases from the current state.
    
    Args:
        state: StateManager instance
        
    Returns:
        List of test case dictionaries
    """
    try:
        if hasattr(state, 'test_cases') and state.test_cases:
            logger.info(f"Found {len(state.test_cases)} test cases available for template generation")
            return state.test_cases
        else:
            logger.warning("No test cases available in current state")
            return []
            
    except Exception as e:
        logger.error(f"Failed to retrieve available test cases: {e}")
        return []


def format_test_case_display(test_case: Dict[str, Any]) -> str:
    """
    Format test case information for selection display.
    
    Args:
        test_case: Test case dictionary
        
    Returns:
        Formatted display string
    """
    try:
        tc_id = test_case.get('Test Case ID', 'Unknown ID')
        objective = test_case.get('Test Case Objective', 'No objective specified')
        
        # Truncate objective if too long
        if len(objective) > 80:
            objective = objective[:77] + "..."
            
        return f"{tc_id}: {objective}"
        
    except Exception as e:
        logger.error(f"Failed to format test case display: {e}")
        return "Unknown Test Case"


def validate_template_generation_inputs(template_script: Dict[str, Any], 
                                      target_test_case: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Validate inputs for template-based script generation.
    
    Args:
        template_script: The selected template script
        target_test_case: The target test case for generation
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    try:
        # Validate template script
        if not template_script:
            return False, "No template script selected"
            
        if not template_script.get('content'):
            return False, "Template script has no content"
            
        if template_script.get('type') != 'optimized':
            return False, "Selected script is not an optimized template"
            
        # Validate target test case
        if not target_test_case:
            return False, "No target test case selected"
            
        if not target_test_case.get('Test Case ID'):
            return False, "Target test case has no ID"
            
        if not target_test_case.get('Steps'):
            return False, "Target test case has no steps defined"
            
        logger.info("Template generation inputs validated successfully")
        return True, ""
        
    except Exception as e:
        error_msg = f"Validation failed: {e}"
        logger.error(error_msg)
        return False, error_msg


def create_template_generation_filename(template_script: Dict[str, Any], 
                                       target_test_case: Dict[str, Any]) -> str:
    """
    Create a unique filename for template-generated script.
    
    Args:
        template_script: The template script used
        target_test_case: The target test case
        
    Returns:
        Unique filename string
    """
    try:
        template_tc_id = template_script.get('test_case_id', 'unknown')
        target_tc_id = target_test_case.get('Test Case ID', 'unknown')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Clean IDs for filename use
        template_tc_clean = template_tc_id.replace(' ', '_').replace('/', '_')
        target_tc_clean = target_tc_id.replace(' ', '_').replace('/', '_')
        
        filename = f"template_generated_{target_tc_clean}_from_{template_tc_clean}_{timestamp}.py"
        
        logger.info(f"Created template generation filename: {filename}")
        return filename
        
    except Exception as e:
        logger.error(f"Failed to create template generation filename: {e}")
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"template_generated_unknown_{timestamp}.py"


def extract_template_structure_info(template_content: str) -> Dict[str, Any]:
    """
    Extract structural information from template script for AI context.
    
    Args:
        template_content: The template script content
        
    Returns:
        Dictionary with template structure information
    """
    try:
        lines = template_content.splitlines()
        
        structure_info = {
            'total_lines': len(lines),
            'import_statements': [],
            'fixture_definitions': [],
            'test_functions': [],
            'helper_functions': [],
            'class_definitions': [],
            'has_browser_setup': False,
            'has_error_handling': False,
            'has_assertions': False,
            'locator_strategies': set()
        }
        
        for line in lines:
            line_stripped = line.strip()
            
            # Import statements
            if line_stripped.startswith('import ') or line_stripped.startswith('from '):
                structure_info['import_statements'].append(line_stripped)
                
            # Fixture definitions
            elif '@pytest.fixture' in line_stripped or 'def setup_' in line_stripped:
                structure_info['fixture_definitions'].append(line_stripped)
                
            # Test functions
            elif line_stripped.startswith('def test_'):
                structure_info['test_functions'].append(line_stripped)
                
            # Helper functions
            elif line_stripped.startswith('def ') and not line_stripped.startswith('def test_'):
                structure_info['helper_functions'].append(line_stripped)
                
            # Class definitions
            elif line_stripped.startswith('class '):
                structure_info['class_definitions'].append(line_stripped)
                
            # Browser setup detection
            elif 'webdriver' in line_stripped.lower() or 'browser' in line_stripped.lower():
                structure_info['has_browser_setup'] = True
                
            # Error handling detection
            elif 'try:' in line_stripped or 'except' in line_stripped:
                structure_info['has_error_handling'] = True
                
            # Assertion detection
            elif 'assert' in line_stripped:
                structure_info['has_assertions'] = True
                
            # Locator strategies
            if 'find_element' in line_stripped:
                if 'By.ID' in line_stripped:
                    structure_info['locator_strategies'].add('ID')
                elif 'By.CSS_SELECTOR' in line_stripped:
                    structure_info['locator_strategies'].add('CSS_SELECTOR')
                elif 'By.XPATH' in line_stripped:
                    structure_info['locator_strategies'].add('XPATH')
                elif 'By.NAME' in line_stripped:
                    structure_info['locator_strategies'].add('NAME')
        
        # Convert set to list for JSON serialization
        structure_info['locator_strategies'] = list(structure_info['locator_strategies'])
        
        logger.info(f"Extracted template structure info: {len(structure_info['test_functions'])} test functions, "
                   f"{len(structure_info['helper_functions'])} helper functions")
        
        return structure_info
        
    except Exception as e:
        logger.error(f"Failed to extract template structure info: {e}")
        return {
            'total_lines': 0,
            'import_statements': [],
            'fixture_definitions': [],
            'test_functions': [],
            'helper_functions': [],
            'class_definitions': [],
            'has_browser_setup': False,
            'has_error_handling': False,
            'has_assertions': False,
            'locator_strategies': []
        }
