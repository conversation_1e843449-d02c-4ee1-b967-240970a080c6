{"test_case_id": "TC_001", "timestamp": "2025-05-31T00:13:42.927519", "data_hash": "2a778fe82c5270bc4b66e3b5fdd9b8a1fd5f329b59a93575cd7e53bfef6ae9bf", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_4", "update_timestamp": "2025-05-31T00:13:13.329411", "step_count": 4, "step_no": "4", "script_file_path": "generated_tests\\test_TC_001_4_1748675593_merged.py", "script_content_length": 3192, "step_specific_file": "generated_tests\\test_TC_001_4_1748675593_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-31T00:13:13.329411", "url_tracking_update": {"step_no": "4", "timestamp": "2025-05-31T00:13:42.927519", "operation": "url_tracking_update"}}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "https://the-internet.herokuapp.com/login", "test_data_param": "{{login_page_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250531_000811", "step_no": "1", "action": "test_execution_complete"}], "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "url_capture_timestamp": "2025-05-31T00:08:18.463333", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748675274_merged.py", "_script_content_length": 1271, "_script_generation_timestamp": "2025-05-31T00:07:54.723751", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748675274_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "username", "test_data_param": "{{username}}", "expected_result": "username_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid user ID in the designated field", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250531_000923", "step_no": "2", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-31T00:09:31.721763", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_2": "tomsmith"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-31T00:08:52.859072", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_2_1748675343_merged.py", "_script_content_length": 1943, "_script_generation_timestamp": "2025-05-31T00:09:03.443465", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_2_1748675343_step_only.py"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid password in the designated field", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250531_001029", "step_no": "3", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-31T00:10:39.652049", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_3": "SuperSecretPassword!"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-31T00:09:58.833168", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_3_1748675410_merged.py", "_script_content_length": 2721, "_script_generation_timestamp": "2025-05-31T00:10:10.977643", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_3_1748675410_step_only.py"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "url_contains", "condition": "", "timeout": 10, "step_description": "Click the login button", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250531_001332", "step_no": "4", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-31T00:13:42.927519", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_4_1748675593_merged.py", "_script_content_length": 3192, "_script_generation_timestamp": "2025-05-31T00:13:13.329411", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_4_1748675593_step_only.py"}]}